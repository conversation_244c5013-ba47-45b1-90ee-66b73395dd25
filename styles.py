# styles.py - Updated with cross-browser compatibility fixes

from string import Template
from textwrap import dedent

class HTML_Template:
    base_style = Template(
        dedent(
            """
            <style>
                $css
            </style>"""
        )
    )

# Modern dark theme color palette
COLORS = {
    'primary': '#3b82f6',          # Bright blue
    'primary_dark': '#2563eb',     # Darker blue
    'primary_light': '#60a5fa',    # Light blue
    'background': '#0f172a',       # Dark blue-gray
    'surface': '#1e293b',          # Lighter blue-gray
    'surface_dark': '#334155',     # Medium blue-gray
    'text': '#f8fafc',            # Almost white
    'text_secondary': '#94a3b8',  # Light gray
    'border': '#475569',          # Medium gray
    'excel_green': '#217346',     # Excel brand green
    'calculate_blue': '#4361ee',  # Complementary blue for calculate button
    'next_button': '#3b82f6',     # Explicitly define next button color (blue)
    "main_background": '#2D3748',
    "sidebar_color" : '#1e293b',


}

def get_base_styles():
    return f"""


        /* Control Panel Card */
        .st-key-control_section,  .st-key-db_viewer, .st-key-results_section {{
            background-color: {COLORS['background']} !important;
            border: 1px solid {COLORS['border']} !important;
            border-radius: 1.5rem;
            padding: 1.5rem !important;
            margin: 1rem auto !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            height: 100%;
        }}

        .st-emotion-cache-14mht0b {{
            background:{COLORS['main_background']} !important;

        }}

        .st-key-db {{

            background-color: {COLORS['sidebar_color']} !important;
            border: 1px solid {COLORS['border']} !important;
            border-radius: 1.5rem;
            margin: 1rem auto !important;
            padding: 1.5rem !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 98%;
            overflow: hidden;

        }}


        .st-key-data_editor {{
        
            min-width: 100%;
            width: max-content;
                width: 100%;
            flex-grow: 1;
        }}

        .st-key-excel_opener button, .st-key-add_to_excel button {{
            background-color: {COLORS['excel_green']} !important;
            border: none !important;
            color: white !important;
        }}

        .st-key-excel_opener button:hover, .st-key-add_to_excel button:hover {{
            background-color: #1a5c38 !important;
            border: none !important;
        }}

                /* Calculate Button */
        .st-key-calculate_button button {{
            background-color: {COLORS['calculate_blue']} !important;
            border: none !important;
            color: white !important;
        }}

        .st-key-calculate_button button:hover {{
            background-color: #3851d1 !important;
            border: none !important;
        }}

        /*.st-key-add_to_excel_container {{
        
            display: flex;
            flex-direction: column;
            align-items: center;
        }}
        */


.st-key-add_to_excel_container {{
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    padding: 16px;
    border-radius: 8px;
    margin-top: 16px;
}}

/* Style for buttons inside the container */
.st-key-add_to_excel_container .stButton {{
    width: 100%;
}}





/* If you want buttons in a horizontal row */
.st-key-add_to_excel_container.horizontal {{
    flex-direction: row;
    gap: 12px;
    justify-content: flex-end;
}}

/* If you want a two-column grid layout */
.st-key-add_to_excel_container.grid {{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}}




        

        .st-key-close_results {{
            transition: all 0.3s ease;

        }}

        .st-key-close_results button:hover {{
                    background-color: #e53e3e !important; /* Red background on hover */
                    color: white !important; /* White text/X on hover */
                    border: 1px solid white !important; /* White border on hover */
                }}



        div[role="dialog"]:has(.st-key-large){{
                    width:85%;
            }}


        .st-emotion-cache-1oyho88 {{
        background-color: {COLORS['main_background']} !important;
        }}
        

    """

def apply_styles(st):
    """Apply the styles to the Streamlit app"""
    css = get_base_styles()
    st.markdown(HTML_Template.base_style.substitute(css=css), unsafe_allow_html=True)

# Hide Streamlit's default elements
hide_st_style = """

    <style>
        /* Hide the hamburger menu */
        #MainMenu {visibility: hidden !important;}
        
        /* Hide the header */
        header[data-testid="stHeader"] {visibility: hidden !important;}
        
        /* Hide the toolbar */
        div[data-testid="stToolbar"] {visibility: hidden !important;}
        
        /* Hide the decoration */
        div[data-testid="stDecoration"] {visibility: hidden !important;}
        
        /* Hide the status widget */
        div[data-testid="stStatusWidget"] {visibility: hidden !important;}
        
        /* Adjust padding */
        .main .block-container {padding-top: 0rem !important;}
        
        /* IMPORTANT: Do NOT hide the collapsed control - this is what makes the sidebar toggleable */
        /* button[data-testid="collapsedControl"] should remain visible */
    </style>

"""