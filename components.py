import streamlit as st
from state import (
    update_session_state, 
    get_calculation_state, 
    set_calculation_state,
    get_db_viewer_state,
    update_db_viewer_state,
    DATABASE_VIEWER_STATE
)
import pandas as pd
import io
from utils.Control_Panel_25 import run
from load_database import load_database, catogeries, load_database_hier, load_input_table
import time
import os
import webbrowser
import openpyxl
import pyarrow.parquet as pq
import threading

from config import REPL_DF_PATH, OUTPUT_DF_PATH
from styles import COLORS

from piechart_component import create_piechart

repl_df = REPL_DF_PATH
output_df_path = OUTPUT_DF_PATH

excel_path = os.path.abspath("data/selected_tpns.xlsx")

def delete_excel_content():
    """Delete content from Excel file starting from A2 and B2"""
    try:
        #excel_path = os.path.abspath("data/selected_tpns.xlsx")
        wb = openpyxl.load_workbook(excel_path)
        sheet = wb.active
        
        # Find the last row with data
        max_row = sheet.max_row
        
        # Clear content from A2 and B2 to the end
        for row in range(2, max_row + 1):
            sheet.cell(row=row, column=1).value = None
            sheet.cell(row=row, column=2).value = None
        
        # Save the workbook
        wb.save(excel_path)

        st.success("Input content has been cleared successfully!")
        time.sleep(3)
        # Refresh metrics
        st.rerun()
    except Exception as e:
        st.error(f"Error clearing Input content: {str(e)}")

@st.fragment
def show_temp_success():
    """Show temporary success message and update metrics"""
    success_placeholder = st.empty()
    button_placeholder = st.empty()
    success_placeholder.success("File uploaded and saved successfully!")
    
    # Get the countries from the newly uploaded file and update metrics immediately
    try:
        df = pd.read_excel(excel_path)
        affected_countries = set(df['country'].unique())
        
        # Update metrics state with affected countries
        st.session_state['animation_cycle'] = st.session_state.get('animation_cycle', 0) + 1
        st.session_state['updated_metrics'] = affected_countries
        
        
        # Schedule the clearing of highlights
        def clear_highlights():
            time.sleep(2)
            if 'updated_metrics' in st.session_state:
                st.session_state.updated_metrics = set()
            st.rerun()
        
        threading.Thread(target=clear_highlights).start()
        
    except Exception:
        pass
    
    time.sleep(3)
    success_placeholder.empty()
    button_placeholder.empty()

def show_step_one():
    """Display Step 1: Input Form with Excel button"""
    # Initialize the upload_complete flag if it doesn't exist
    if 'upload_complete' not in st.session_state:
        st.session_state.upload_complete = False
        
    with st.container(border=True):
        st.markdown("### Step 1: Input Form")
        
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            # Track the previous file state
            previous_file = st.session_state.get('previous_file', None)
            
            uploaded_file = st.file_uploader(
                "Upload Excel file (columns: country, tpnb)",
                type=["xlsx"],
                key="excel_uploader",
                disabled=st.session_state.get('calculation_running', False)
            )
            
            # Handle file deletion
            if uploaded_file is None and previous_file is not None:
                st.session_state['previous_file'] = None
                st.session_state['animation_cycle'] = st.session_state.get('animation_cycle', 0) + 1
                st.session_state['updated_metrics'] = set()  # Clear the metrics
                st.rerun()
            
            # Only process if there's a new file or the file has changed
            if uploaded_file is not None and uploaded_file != previous_file:
                try:
                    df = pd.read_excel(uploaded_file)
                    
                    # Validate columns
                    required_columns = ["country", "tpnb"]
                    if not all(col in df.columns for col in required_columns):
                        st.error("Excel file must contain exactly 2 columns: 'country' and 'tpnb'")
                        return
                    
                    # Validate countries
                    valid_countries = ["CZ", "HU", "SK"]
                    invalid_countries = df[~df["country"].isin(valid_countries)]["country"].unique()
                    if len(invalid_countries) > 0:
                        st.error(f"Invalid countries found: {', '.join(invalid_countries)}. Only CZ, HU, SK are allowed.")
                        return
                    
                    # Save to the expected location
                    df.to_excel(excel_path, index=False, sheet_name="country_tpnb")
                    
                    # Update previous file state
                    st.session_state['previous_file'] = uploaded_file
                    
                    # Show temporary success message and handle metrics update
                    show_temp_success()
                    st.rerun()  # Force refresh to update metrics
                    
                except Exception as e:
                    st.error(f"Error processing file: {str(e)}")
                    return

            # Add Show Missing Products button outside the if block
            if uploaded_file is not None:  # Only show button if file is uploaded
                if st.button("Show Missing Products", key="validate_tpnbs"):
                    with st.spinner('Validating TPNBs...'):
                        try:
                            # Read the uploaded Excel file
                            df = pd.read_excel(excel_path)
                            
                            # Read the main dataset
                            main_df = pq.read_table(repl_df).to_pandas()
                            
                            # Group by country and find missing TPNBs
                            missing_by_country = {}
                            for country in df['country'].unique():
                                # Get TPNBs for this country from uploaded file
                                country_tpnbs = set(df[df['country'] == country]['tpnb'].unique())
                                
                                # Get TPNBs that exist for this specific country in main dataset
                                existing_country_tpnbs = set(
                                    main_df[main_df['country'] == country]['tpnb'].unique()
                                )
                                
                                # Find missing TPNBs (those not in main dataset for this country)
                                missing_tpnbs = country_tpnbs - existing_country_tpnbs
                                if missing_tpnbs:
                                    missing_by_country[country] = sorted(missing_tpnbs)

                            @st.dialog("Missing Products Analysis")
                            def show_validation_results():
                                if missing_by_country:
                                    # Summary metrics at the top
                                    total_missing = sum(len(tpnbs) for tpnbs in missing_by_country.values())
                                    total_products = len(df['tpnb'].unique())
                                    
                                    # Create three columns for metrics
                                    col1, col2, col3 = st.columns(3)
                                    
                                    with col1:
                                        st.metric("Total Products", total_products)
                                    with col2:
                                        st.metric("Missing Products", total_missing)
                                    with col3:
                                        st.metric("Coverage", f"{((total_products - total_missing)/total_products)*100:.1f}%")
                                    
                                    st.divider()
                                    
                                    # Country-specific sections with modern styling
                                    for country in ['CZ', 'HU', 'SK']:
                                        if country in missing_by_country:
                                            with st.expander(f"🔍 {country} - {len(missing_by_country[country])} missing TPNBs"):
                                                # Create a DataFrame for the country's missing TPNBs
                                                country_df = pd.DataFrame({
                                                    'TPNB': missing_by_country[country]
                                                })
                                                
                                                # Add information about where these TPNBs exist (if anywhere)
                                                country_df['Found In'] = country_df['TPNB'].apply(
                                                    lambda x: ', '.join(
                                                        main_df[main_df['tpnb'] == x]['country'].unique()
                                                    ) or 'Not found anywhere'
                                                )
                                                
                                                # Show the data in a modern table
                                                st.dataframe(
                                                    country_df,
                                                    hide_index=True,
                                                    use_container_width=True,
                                                    column_config={
                                                        "TPNB": st.column_config.NumberColumn(
                                                            "TPNB",
                                                            help="Missing product identifier",
                                                            format="%d"
                                                        ),
                                                        "Found In": st.column_config.TextColumn(
                                                            "Found In Other Countries",
                                                            help="Countries where this TPNB exists"
                                                        )
                                                    }
                                                )
                                    
                                    # Export functionality
                                    st.divider()
                                    export_col1, export_col2 = st.columns([3, 1])
                                    with export_col1:
                                        st.info("💡 You can export the missing TPNBs to continue your analysis offline.")
                                    with export_col2:
                                        # Create Excel file in memory
                                        output = io.BytesIO()
                                        with pd.ExcelWriter(output, engine='openpyxl') as writer:
                                            for country, tpnbs in missing_by_country.items():
                                                # Create DataFrame with both TPNB and where it's found
                                                export_df = pd.DataFrame({
                                                    'TPNB': tpnbs,
                                                    'Found In Other Countries': [
                                                        ', '.join(
                                                            main_df[main_df['tpnb'] == tpnb]['country'].unique()
                                                        ) or 'Not found anywhere'
                                                        for tpnb in tpnbs
                                                    ]
                                                })
                                                export_df.to_excel(
                                                    writer, 
                                                    sheet_name=country, 
                                                    index=False
                                                )
                                        
                                        st.download_button(
                                            "📥 Export",
                                            data=output.getvalue(),
                                            file_name="missing_tpnbs.xlsx",
                                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                            use_container_width=True
                                        )
                                else:
                                    st.success("✅ All products were found in their respective countries!")
                                    st.balloons()
                            
                            # Show the dialog
                            show_validation_results()
                                
                        except Exception as e:
                            st.error(f"Error validating TPNBs: {str(e)}")

        with col3:
            st.write("")
            st.write("")
            st.write("")
            if st.button("Next →", 
                        type="primary",
                        key="step_one_next",
                        use_container_width=True,
                        disabled=st.session_state.get('calculation_running', False)):
                st.session_state.current_step = 1
                st.rerun()


# Mapping for REPL Type display values
repl_type_mapping = {
    "none": "None",
    "srp": "SRP",
    "nsrp": "NSRP",
    "full_pallet": "Full Pallet",
    "mu": "Half Pallet",
    "split_pallet": "Split Pallet",
    "icream_nsrp": "Ice Cream NSRP",
    "single_pick": "Single Pick"
}

def format_option(option):
    labels = {
    "none": "None",
    "srp": "SRP",
    "nsrp": "NSRP",
    "full_pallet": "Full Pallet",
    "mu": "Half Pallet",
    "split_pallet": "Split Pallet",
    "icream_nsrp": "Ice Cream NSRP",
    "single_pick": "Single Pick"
}
    return labels.get(option, option)

def setup_variables():
    """Display Step 2: Settings Configuration"""
    with st.container(border=True):
        st.markdown("### Step 2: Settings Configuration")
        
        # Check current store_level value to determine layout
        current_store_level = st.session_state.variables['store_level']
        
        # Create columns based on the current store_level
        if current_store_level:
            # Four columns when store_level is True
            main_col1, main_col2, main_col3, main_col4 = st.columns([1, 1, 1, 1])
        else:
            # Two columns when store_level is False
            main_col1, main_col2 = st.columns([1, 1])
        
        # Left column - REPL TYPE
        with main_col1:
            st.markdown("##### REPL TYPE")
            repl_type = st.radio(
                "Select REPL Type",
                options=["none", "srp", "nsrp", "full_pallet", "mu", 
                        "split_pallet"],
                index=["none", "srp", "nsrp", "full_pallet", "mu", 
                       "split_pallet"].index(
                           st.session_state.variables['repl_type']),
                label_visibility="collapsed",
                disabled=st.session_state.get('calculation_running', False),
                format_func=format_option
            )

        # Second column - MODIFIERS
        with main_col2:
            st.markdown("##### MODIFIERS")
            # Keep the Store Level toggle with other modifiers
            store_level = st.toggle(
                "Store Level",
                value=current_store_level,
                disabled=st.session_state.get('calculation_running', False),
                help="In case you need calculation on Store Level  \n(Be aware it is time consuming process and Don't choose more than 1000 products!)",
                key="store_level_toggle"
            )
            
            # Check if store_level changed and force rerun if needed
            if store_level != current_store_level:
                st.session_state.variables['store_level'] = store_level
                st.rerun()
                
            # Other modifiers
            volume_modifier = st.toggle(
                "Volume Modifier",
                value=st.session_state.variables['volume_modifier'],
                disabled=True #st.session_state.get('calculation_running', False)
            )
            case_cap_modifier = st.toggle(
                "Case Capacity Modifier",
                value=st.session_state.variables['case_cap_modifier'],
                disabled=True #st.session_state.get('calculation_running', False)
            )
            shelf_capacity_modifier = st.toggle(
                "Shelf Capacity Modifier",
                value=st.session_state.variables['shelf_capacity_modifier'],
                disabled=True #st.session_state.get('calculation_running', False)
            )
        
        # Initialize opening_type and chunk_size with defaults
        opening_type = "none"
        chunk_size = st.session_state.variables['chunk_size']
        
        # Only create the OPENING TYPE and CHUNK SIZE columns if store_level is True
        if current_store_level:
            with main_col3:
                st.markdown("##### OPENING TYPE")
                opening_type = st.radio(
                    "Select Opening Type",
                    options=["none", "Perforated box", "Shrink", "Tray",
                            "Tray + Hood", "Tray + Shrink","Returnable Plastic Crate"],
                    index=["none", "Perforated box", "Shrink", "Tray",
                        "Tray + Hood", "Tray + Shrink", "Returnable Plastic Crate"].index(
                            st.session_state.variables['opening_type'] if st.session_state.variables['opening_type'] != None else "none"),
                    label_visibility="collapsed",
                    disabled=st.session_state.get('calculation_running', False)
                )

            # CHUNK SIZE - now only showing when store_level is True
            with main_col4:
                st.markdown("##### CHUNK SIZE")
                chunk_size = st.number_input(
                    "Enter Chunk Size",
                    min_value=100,
                    max_value=1000,
                    value=st.session_state.variables['chunk_size'],
                    step=100,
                    label_visibility="collapsed",
                    disabled=st.session_state.get('calculation_running', False)
                )
        
        # Navigation buttons
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("← Back", 
                        type="secondary",
                        key="step_two_back",
                        use_container_width=True,
                        disabled=st.session_state.get('calculation_running', False)):
                st.session_state.current_step = 0
                st.rerun()
        
        with col3:
            if st.button("Next →", 
                        type="primary",
                        key="step_two_next",
                        use_container_width=True,
                        disabled=st.session_state.get('calculation_running', False)):
                update_session_state({
                    'repl_type': repl_type,
                    'opening_type': opening_type if store_level else "none",
                    'chunk_size': chunk_size,
                    'volume_modifier': volume_modifier,
                    'case_cap_modifier': case_cap_modifier,
                    'shelf_capacity_modifier': shelf_capacity_modifier,
                    'store_level': store_level
                })
                st.session_state.settings_saved = True
                st.session_state.show_variables = True
                st.session_state.current_step = 2
                st.rerun()

def show_variables_card(show_calculate=True):
    """Display Step 3: Review Settings and Calculate"""
    if st.session_state.show_variables:
        with st.container(border=True):
            st.markdown("### Step 3: Review & Calculate")
            repl_type_display = repl_type_mapping.get(st.session_state.variables['repl_type'], '❌')
            
            # Create columns for better layout
            col1, col2 = st.columns(2)
            
            # Get store level value
            store_level = st.session_state.variables['store_level']
            
            with col1:
                # Show current settings
                st.markdown("##### Current Settings:")
                repl_type = st.session_state.variables['repl_type']
                
                # Show different settings based on store_level
                if store_level:
                    # When store_level is ON, show all settings
                    st.markdown(f"""
                    - **REPL Type:** {'❌' if repl_type == 'none' else repl_type_mapping.get(repl_type, repl_type)}
                    - **Opening Type:** {st.session_state.variables['opening_type'] if st.session_state.variables['opening_type'] != 'none' else '❌'}
                    - **Chunk Size:** {st.session_state.variables['chunk_size']}
                    """)
                else:
                    # When store_level is OFF, only show REPL Type
                    st.markdown(f"""
                    - **REPL Type:** {'❌' if repl_type == 'none' else repl_type_mapping.get(repl_type, repl_type)}
                    """)
            
            with col2:
                # Show modifiers
                st.markdown("##### Modifiers:")
                
                # Always show Store Level toggle status
                if store_level:
                    # When store_level is ON, only show enabled modifiers or those that are checked
                    # Start with Store Level which is always shown
                    modifier_text = f"- **Store Level:** {'✅' if store_level else '❌'}\n"
                    
                    # Only add other modifiers if they're enabled or checked
                    if not st.session_state.variables['volume_modifier']:
                        # Skip disabled and unchecked modifiers
                        pass
                    else:
                        modifier_text += f"- **Volume Modifier:** ✅\n"
                        
                    if not st.session_state.variables['case_cap_modifier']:
                        # Skip disabled and unchecked modifiers
                        pass
                    else:
                        modifier_text += f"- **Case Capacity Modifier:** ✅\n"
                        
                    if not st.session_state.variables['shelf_capacity_modifier']:
                        # Skip disabled and unchecked modifiers
                        pass
                    else:
                        modifier_text += f"- **Shelf Capacity Modifier:** ✅\n"
                    
                    st.markdown(modifier_text)
                else:
                    # When store_level is OFF, only show Store Level status
                    st.markdown(f"""
                    - **Store Level:** {'✅' if store_level else '❌'}
                    """)
            
            # Navigation and calculation buttons
            col1, col2, col3 = st.columns([1, 1, 1])
            with col1:
                if st.button("← Back", 
                            type="secondary",
                            key="step_three_back",
                            use_container_width=True,
                            disabled=st.session_state.get('calculation_running', False)):
                    st.session_state.current_step = 1
                    st.rerun()
            
            with col3:
                is_calculating = st.session_state.get('calculation_running', False)
                if st.button('Calculate',
                            type='primary',
                            key='calculate_button',
                            icon=':material/sports_score:', #sports_score
                            use_container_width=True,
                            disabled=is_calculating):
                    st.session_state.calculation_running = True
                    st.rerun()

def get_tpnb_count_by_country():
    """Get TPNB count by country from Excel file"""
    try:
        import pandas as pd
        import os
        
        file_path = "data/selected_tpns.xlsx"
        sheet_name = "country_tpnb"
        
        if not os.path.exists(file_path):
            return None, "File not found: data/selected_tpns.xlsx"
            
        # Read Excel file
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=0)
        
        # Count TPNBs by country
        country_counts = df['country'].value_counts()
        
        # Return the counts directly as a dictionary
        return {
            'CZ': country_counts.get('CZ', 0),
            'HU': country_counts.get('HU', 0),
            'SK': country_counts.get('SK', 0)
        }, None
        
    except Exception as e:
        return None, f"Error reading data: {str(e)}"

st.cache_data()
def calculate_based_on_result_data(df, repl_type_v):
    """Calculate based on the result data"""

    filter_tpn = pd.read_excel(excel_path, sheet_name="country_tpnb")
    
    ce_df = pd.DataFrame()

    for c in filter_tpn.country.unique().tolist():


        dict_list = (
            filter_tpn[(filter_tpn.country == c)].groupby("country")["tpnb"]
            .apply(lambda s: s.tolist())
            .to_dict()
        )
        for k, v in dict_list.items():
            result = pq.read_table(
                df,
                filters=[("Tpnb", "in", v), ("country", "=", k), ("repl_type_change", "=", repl_type_v)],
            ).to_pandas()

        ce_df = pd.concat([ce_df, result], ignore_index=True)
    ce_df.rename(columns={"country": "Country"}, inplace=True)


    return ce_df




def perform_calculation():
    """Execute the calculation if it's running"""
    if st.session_state.get('calculation_running', False):
        try:
            vars = st.session_state.variables
            
            # Create status message and spinner
            with st.spinner('Running calculation...', show_time=True):
                try:
                    if not st.session_state.variables['store_level']:

                        result = calculate_based_on_result_data(
                            output_df_path,
                            repl_type_v=vars['repl_type'])
                    else:
                        print("Eljut idáig")
                        # Run the actual calculation
                        details, result = run(
                            repl_dataset_f= repl_df ,
                            repl_type_v=vars['repl_type'],
                            opening_type_v=vars['opening_type'],
                            volume_modifier_v=vars['volume_modifier'],
                            case_cap_modifier_v=vars['case_cap_modifier'],
                            shelf_capacity_modifier_v=vars['shelf_capacity_modifier'],
                            chunk_size_v=vars['chunk_size']
                        )
                        
                    # Only update state if calculation completes successfully
                    st.session_state.result = result
                    try:
                        st.session_state.details = details
                    except:
                        pass
                    st.session_state.calculation_running = False
                    st.session_state.calculation_complete = True
                    st.session_state.show_error = False
                    st.session_state.error_details = None


                    # Custom CSS to style the toast
                    st.markdown("""
                    <style>
                        .stToast {
                            background-color: #28a745 !important;
                            color: white !important;
                            border-radius: 15px !important;
                        }
                    </style>
                    """, unsafe_allow_html=True)
                    
                    # Show success message
                    st.toast('Calculation completed successfully!')
                    time.sleep(3)
                    st.rerun()
                    
                except Exception as e:
                    error_msg = str(e)
                    
                    # Create user-friendly error messages for common errors
                    if "Permission denied" in error_msg:
                        friendly_msg = "⚠️ Error: Excel file is currently open. Please close the file and try again."
                        steps = """
                        1. Close the Excel file if it's open
                        2. Make sure no other program is using the file
                        3. Click 'Retry Calculation' below
                        """
                    else:
                        friendly_msg = f"⚠️ Error during calculation: {error_msg}"
                        steps = """
                        1. Check your input parameters
                        2. Try running the calculation again
                        3. If the error persists, contact support
                        """
                    
                    # Store error details in session state
                    st.session_state['error_details'] = {
                        'message': friendly_msg,
                        'technical': error_msg,
                        'steps': steps
                    }
                    st.session_state['show_error'] = True
                    st.session_state.calculation_running = False
                    st.session_state.calculation_complete = False
                    st.rerun()
        
        except Exception as outer_e:
            outer_error_msg = str(outer_e)
            st.session_state['error_details'] = {
                'message': f'⚠️ Unexpected error: {outer_error_msg}',
                'technical': outer_error_msg,
                'steps': """
                1. Check your input parameters
                2. Try running the calculation again
                3. Contact support if the issue persists
                """
            }
            st.session_state['show_error'] = True
            st.session_state.calculation_running = False
            st.session_state.calculation_complete = False
            st.rerun()

@st.dialog("Input Table Content")
def show_input_table_dialog():
    # Create tabs for data view and visualizations
    tab1, tab2 = st.tabs(["📋 Data Table", "📊 Visualizations"])
    
    with tab1:
        with st.container(key="large"):
            try:
                # Load the Excel file
                df = pd.read_excel(excel_path)
                filter_dict = df[['country', 'tpnb']].drop_duplicates().groupby(["country"],observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()
                final_df = load_input_table(repl_df, filter_dict)
                
                # Show the table using the same styling as database viewer
                st.dataframe(
                    final_df,
                    use_container_width=True,
                    hide_index=True,
                    column_config={
                        "tpnb": st.column_config.NumberColumn(
                            "TPNB",
                            format="%d"
                        ),
                    }
                )
            except Exception as e:
                st.error(f"Error loading input table: {str(e)}")
    
    with tab2:
        try:
            df = pd.read_excel(excel_path)
            final_df = load_input_table(repl_df, filter_dict)
            create_piechart(final_df)
        except Exception as e:
            st.error(f"Error creating visualizations: {str(e)}")


def show_metrics_card():
    """Display the metrics card with country flags"""
    
    # Define the metric style (keep your existing style)
    metric_style = """
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-radius: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    """
    
    # Calculate summary counts from input file (keep your existing logic)
    try:
        df = pd.read_excel(excel_path)
        summary_counts = {
            'CZ': df[df['country'] == 'CZ']['tpnb'].nunique(),
            'HU': df[df['country'] == 'HU']['tpnb'].nunique(),
            'SK': df[df['country'] == 'SK']['tpnb'].nunique()
        }
    except Exception:
        # If file doesn't exist or is empty, set counts to 0
        summary_counts = {'CZ': 0, 'HU': 0, 'SK': 0}
    
    with st.container(key="metrics_card"):
        flag_url_hungary = "https://upload.wikimedia.org/wikipedia/commons/c/c1/Flag_of_Hungary.svg"
        flag_url_czech = "https://upload.wikimedia.org/wikipedia/commons/c/cb/Flag_of_the_Czech_Republic.svg"
        flag_url_slovakia = "https://upload.wikimedia.org/wikipedia/commons/e/e6/Flag_of_Slovakia.svg"

        st.write("")
        st.write("")
        st.write("")
        
        # Add just the highlight animation CSS
        st.markdown("### TPNB by country", help="Summary of TPNB counts by country")
        st.markdown("###### Nr of Actual Selected TPNB in Excel")
        st.write("")  # Add spacing
        
        summary_counts, error = get_tpnb_count_by_country()
        if error:
            st.error(f"Error reading data: {error}")
        elif summary_counts is not None:
            # Common style for all metrics
            metric_style = """
                background-color: #1e293b;
                border: 1px solid #475569;
                border-radius: 5px;
                padding: 15px;
                margin-bottom: 15px;
                display: flex;
                align-items: center;
                transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
                cursor: pointer;
            """
            
            # Get animation cycle from session state
            animation_cycle = st.session_state.get('animation_cycle', 0)
            
            # Add hover effect and highlight animation with unique animation name
            st.markdown(f"""
                <style>
                    [data-testid="stSidebarContent"] .st-key-metrics_card div[data-testid="stMarkdownContainer"] > div {{
                        transition: all 0.3s ease-in-out;
                        position: relative;
                        z-index: 1;
                    }}
                    
                    [data-testid="stSidebarContent"] .st-key-metrics_card div[data-testid="stMarkdownContainer"] > div:hover {{
                        transform: translateY(-2px);
                        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.15);
                    }}
                    
                    @keyframes highlight_{animation_cycle} {{
                        0% {{ 
                            background-color: {COLORS['surface']};
                            box-shadow: 0 0 0 rgba(59, 130, 246, 0);
                        }}
                        50% {{ 
                            background-color: rgba(59, 130, 246, 0.15);
                            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
                        }}
                        100% {{ 
                            background-color: {COLORS['surface']};
                            box-shadow: 0 0 0 rgba(59, 130, 246, 0);
                        }}
                    }}
                    
                    .metric-highlight-{animation_cycle} {{
                        animation: highlight_{animation_cycle} 2s cubic-bezier(0.4, 0, 0.2, 1);
                    }}
                </style>
            """, unsafe_allow_html=True)

            # Get the updated countries from session state
            updated_countries = st.session_state.get('updated_metrics', set())
            
            flag_style = "width: 30px; height: 23px; object-fit: cover; border-radius: 3px;"
            
            
            # Helper function to generate metric HTML
            def create_metric_html(country, flag_url, count):
                # Only add highlight class if this specific country is in updated_countries
                highlight_class = f"metric-highlight-{animation_cycle}" if country in updated_countries else ""
                
                return f"""
                <div style='{metric_style}' class='{highlight_class}'>
                    <div style='margin-right: 15px;'><img src="{flag_url}" style="{flag_style}"/></div>
                    <div style='margin-right: 10px; font-size: 16px; color: #f8fafc;'>{country}</div>
                    <div style='margin-left: auto; font-size: 28px; font-weight: bold; color: #f8fafc;'>{count}</div>
                </div>
                """
            
            # Display metrics
            st.markdown(create_metric_html("CZ", flag_url_czech, summary_counts['CZ']), unsafe_allow_html=True)
            st.markdown(create_metric_html("HU", flag_url_hungary, summary_counts['HU']), unsafe_allow_html=True)
            st.markdown(create_metric_html("SK", flag_url_slovakia, summary_counts['SK']), unsafe_allow_html=True)
        # Add spacing before delete button
        st.write("")
        
        # Add delete button below metrics
        if st.sidebar.button("Delete Input Content", 
                    type="secondary",
                    key="delete_excel", 
                    icon=":material/delete:",
                    help="Delete content from INPUT file",
                    use_container_width=True,
                    disabled=st.session_state.get('calculation_running', False)):
            delete_excel_content()
        
        # Add View Input Table button
        if st.sidebar.button("View Input Table", 
                    type="secondary",
                    key="view_input", 
                    icon="📋",
                    help="View current input table content",
                    use_container_width=True,
                    disabled=st.session_state.get('calculation_running', False)):
            show_input_table_dialog()


def download_results(df, result_type: str):
                # Export buttons
            col1, col2, col3 = st.columns(3)
            
            with col1:
                buffer = io.BytesIO()
                with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
                    df.to_excel(writer, sheet_name='Results', index=False)
                st.download_button(
                    "📥 Download Excel",
                    help=f"Download the calculation results as an Excel file",
                    data=buffer.getvalue(),
                    file_name=f"calculation_results_{result_type}.xlsx",
                    mime="application/vnd.ms-excel",
                )
            # with col3:
            #     try:
            #         hu_nunique = df[df['Country'] == "HU"]['Tpnb'].nunique()
            #         sk_nunique = df[df['Country'] == "SK"]['Tpnb'].nunique()
            #         cz_nunique = df[df['Country'] == "CZ"]['Tpnb'].nunique()

            #         st.write(f"HU nr of unique TPNB: {hu_nunique}")
            #         st.write(f"SK nr of unique TPNB: {sk_nunique}")
            #         st.write(f"CZ nr of unique TPNB: {cz_nunique}")
            #     except:
            #         pass

def show_results_table():
    """Display the calculation results"""
    with st.container(key="results_section"):
        # Add header with close button
        col1, col2 = st.columns([6, 0.20])
        with col1:
            st.subheader("Calculation Results")
        with col2:


            st.markdown(
                """
                <style>

                .st-key-close_results {
                    color: #ffffff;
                    font-size: 20px;
                    background: none;
                    border: none;
                    cursor: pointer;
                }
                </style>
                """, 
                unsafe_allow_html=True
            )

            if st.button("X", key="close_results", help="Close results", kwargs={'className': 'close-button'},
                        disabled=st.session_state.get('calculation_running', False)):
                st.session_state.calculation_complete = False
                st.session_state.result = None
                st.session_state.details = None
                st.rerun()
        
        if st.session_state.details is not None:
            # Create tabs for different views
            tab1, tab2, tab3 = st.tabs(["📉 Total by Countries","📊 TPNB View", "📈 Store+TPNB View"])
        else:
            tab1, tab2= st.tabs(["📉 Total by Countries","📊 TPNB View"])

        with tab1:
            # Handle DataFrame display and export
            result = st.session_state.result
            if isinstance(result, pd.DataFrame):
                df = result
                df = df.groupby(["Country"])[["Yearly GBP", "Yearly GBP_new",
                 "Total Weekly Hours", "Total Weekly Hours_new",
                  "DIFF_in_Weekly_Hours", "DIFF_in_Yearly_GBP"]].sum().reset_index()

            # Calculate totals for numeric columns
            numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
            totals = df[numeric_cols].sum()

            # Create a total row DataFrame
            total_row = pd.DataFrame([totals], columns=numeric_cols)
            total_row.insert(0, 'Country', 'TOTAL')  # Add Country column with "TOTAL" text

            # Concatenate original DataFrame with total row
            df_with_total = pd.concat([df, total_row], ignore_index=True)

            # Create the style
            def highlight_total_row(row):
                if row.Country == 'TOTAL':
                    return ['background-color: #2E4053'] * len(row)
                return [''] * len(row)

            # Display the styled DataFrame
            st.dataframe(
                df_with_total.style
                .apply(highlight_total_row, axis=1)
                .format({
                    'Yearly GBP': "{:,.0f}" ,
                    'Yearly GBP_new': "{:,.0f}",
                    'Total Weekly Hours': '{:.2f}',
                    'Total Weekly Hours_new': '{:.2f}',
                    'DIFF_in_Weekly_Hours': '{:.2f}',
                    'DIFF_in_Yearly_GBP': "{:,.0f}"
                }),
                use_container_width=True,
                height=table_height(df) + 25,  # Call the function with the DataFrame
                hide_index=True
            )           


            download_results(df, "by_countries")

        with tab2:
            # Handle DataFrame display and export
            result = st.session_state.result
            if isinstance(result, pd.DataFrame):
                df = result
            elif isinstance(result, list) and all(isinstance(i, dict) for i in result):
                df = pd.DataFrame(result)
            else:
                st.error("Unexpected data format in results.")
                return
            
            # Display the table
            st.dataframe(
                df.style.format({
                    'Yearly GBP': "{:,.0f}"  ,
                    'Yearly GBP_new': "{:,.0f}" ,
                    'Total Weekly Hours': '{:.2f}',
                    'Total Weekly Hours_new': '{:.2f}',
                    'DIFF_in_Weekly_Hours': '{:.2f}',
                    'DIFF_in_Yearly_GBP': "{:,.0f}" 
                }),
                column_config={
                    "TPNB": st.column_config.NumberColumn(
                        "TPNB",
                        format="%d"
                    ),
                    "Yearly GBP": st.column_config.NumberColumn(
                        "Yearly GBP",

                    ),
                    "Yearly GBP_new": st.column_config.NumberColumn(
                        "Yearly GBP_new",

                    ),
                    "DIFF_in_Yearly_GBP": st.column_config.NumberColumn(
                        "DIFF_in_Yearly_GBP",

                    ),
                    "Total Weekly Hours": st.column_config.NumberColumn(
                        "Total Weekly Hours",
                        format="%.2f"
                    ),
                    "Total Weekly Hours_new": st.column_config.NumberColumn(
                        "Total Weekly Hours_new",
                        format="%.2f"
                    ),
                    "DIFF_in_Weekly_Hours": st.column_config.NumberColumn(
                        "DIFF_in_Weekly_Hours",
                        format="%.2f"
                    ),
                },
                use_container_width=True,
                height=table_height(df),  # Call the function with the DataFrame
                hide_index=True,
            )
            
            download_results(df, "by_TPNB")

        if st.session_state.details is not None:
            with tab3:
                try:
                    # Handle DataFrame display and export
                    result = st.session_state.details
                    if isinstance(result, pd.DataFrame):
                        details = result
                    elif isinstance(result, list) and all(isinstance(i, dict) for i in result):
                        details = pd.DataFrame(result)
                    else:
                        st.error("Unexpected data format in results.")
                        return

                    # Display the table
                    st.dataframe(
                        details,
                        column_config={
                        "Tpnb": st.column_config.NumberColumn(
                            "TPNB",
                            format="%d"  
                        ),
                        "Store": st.column_config.NumberColumn(
                            "Store",
                            format="%d"  
                        )},


                        use_container_width=True,
                        height=400,
                        hide_index=True
                    )


                    download_results(details, "by_Store+TPNB")
                except:
                    pass


def run_calculation():
    """Run the calculation with proper state management"""
    try:
        # Get variables from session state
        vars = st.session_state.variables
        
        # Set initial states
        st.session_state.calculation_start_time = time.time()
        st.session_state.calculation_running = True
        st.session_state.calculation_complete = False
        st.session_state.calculation_error = None
        
        # Force a rerun to update UI (disable buttons)
        st.rerun()
        
    except Exception as e:
        st.error(f'An error occurred during calculation: {str(e)}')
        st.session_state.calculation_running = False
        st.session_state.calculation_complete = False
        st.session_state.calculation_error = str(e)




def handle_category_selection():
    """Handle category selection changes"""
    # Update the persistent state with current selection
    update_db_viewer_state('selected_categories', st.session_state.selected_categories)
    
    # Update the all_selected state based on whether all categories are selected
    all_selected = set(st.session_state.selected_categories) == set(catogeries)
    st.session_state.select_all = all_selected
    update_db_viewer_state('all_selected', all_selected)

def handle_select_all():
    """Handle select all categories toggle"""
    all_selected = st.session_state.select_all
    update_db_viewer_state('all_selected', all_selected)
    
    # Update categories based on toggle state
    if all_selected:
        new_categories = catogeries.copy()
    else:
        new_categories = ["BEERS"]  # Reset to BEERS when deselecting all
    
    st.session_state.selected_categories = new_categories
    update_db_viewer_state('selected_categories', new_categories)

def get_selected_data(df, edited_df):
    """Get selected data from the data editor"""
    # Get rows where selected is True
    selected_rows = edited_df[edited_df['selected'] == True]
    
    # Group by country and get TPNBs, but only include countries with actual data
    result = {}
    grouped_data = selected_rows.groupby('country')['tpnb'].apply(lambda x: x.astype(int).tolist()).to_dict()
    
    # Only include countries that have non-empty lists
    for country, tpnbs in grouped_data.items():
        if tpnbs:  # Only include if the list is not empty
            result[country] = tpnbs
    
    return result

def export_to_excel(selected_data):
    """Export selected data to selected_tpnb.xlsx starting from row 2 in country_tpnb sheet"""
    try:
        import openpyxl
        from openpyxl import load_workbook
        import os
        
        file_path = "data/selected_tpns.xlsx"
        sheet_name = "country_tpnb"
        
        # Create new workbook if it doesn't exist
        if not os.path.exists(file_path):
            wb = openpyxl.Workbook()
            wb.active.title = sheet_name
            wb.save(file_path)
        
        # Load existing workbook
        wb = load_workbook(file_path)
        
        # Create sheet if it doesn't exist
        if sheet_name not in wb.sheetnames:
            ws = wb.create_sheet(sheet_name)
        else:
            ws = wb[sheet_name]
        
        # Clear existing data (from row 2 onwards)
        if ws.max_row > 1:
            for row in range(2, ws.max_row + 1):
                ws.cell(row=row, column=1).value = None
                ws.cell(row=row, column=2).value = None
        
        # Write new data starting from row 2
        row = 2
        for country, tpnbs in selected_data.items():
            for tpnb in tpnbs:
                ws.cell(row=row, column=1).value = country
                ws.cell(row=row, column=2).value = tpnb
                row += 1
        
        # Track only the countries that were actually selected/updated
        affected_countries = set(selected_data.keys())
        
        # Update metrics state with only the affected countries
        st.session_state['animation_cycle'] = st.session_state.get('animation_cycle', 0) + 1
        st.session_state['updated_metrics'] = affected_countries
        
        # Save workbook
        wb.save(file_path)
        
        # Schedule the clearing of highlights
        def clear_highlights():
            time.sleep(3)
            if 'updated_metrics' in st.session_state:
                st.session_state.updated_metrics = set()
            st.rerun()
        
        threading.Thread(target=clear_highlights).start()
        
        return True, None
        
    except Exception as e:
        return False, str(e)

def get_hierarchy_options(df, selected_div=None, selected_dept=None, selected_sect=None):
    """Get options for hierarchy selectors based on selected values"""
    if df is None:
        return [], [], []
        
    if selected_div:
        dept_options = sorted(df[df['division_hier'].isin(selected_div)]['department'].unique())
    else:
        dept_options = []
        
    if selected_dept and selected_div:
        section_options = sorted(df[
            (df['division_hier'].isin(selected_div)) & 
            (df['department'].isin(selected_dept))
        ]['section'].unique())
    else:
        section_options = []
        
    if selected_sect and selected_dept and selected_div:
        group_options = sorted(df[
            (df['division_hier'].isin(selected_div)) & 
            (df['department'].isin(selected_dept)) &
            (df['section'].isin(selected_sect))
        ]['group'].unique())
    else:
        group_options = []
        
    return dept_options, section_options, group_options

def show_database_filters():
    """Show database filters section"""
    with st.container(border=False, key="db_viewer" ): 
        # Create three columns for filters
        filter_col0, filter_col1, filter_col2, filter_col3 = st.columns([0.54,3,0.7,1.2])
        
        with filter_col0:


            st.write("")
            st.write("")
            st.write("")
 
            # Add hierarchy mode toggle
            use_hierarchy = st.checkbox(
                "Hierarchy Mode",
                value=get_db_viewer_state()['use_hierarchy'],
                key='use_hierarchy',
                on_change=lambda: update_db_viewer_state('use_hierarchy', st.session_state.use_hierarchy)
            )

        with filter_col1:

            if use_hierarchy:
                # Load initial data for hierarchy options
                initial_df = load_database_hier(repl_df, 
                                              ['Food Grocery', 'Fresh_Froz_Food', 'NonFood Grocery', 'Hardlines', 'CH Hardlines', 'Centralized Hardline'])
                
                # Division selection
                if 'selected_division' not in st.session_state:
                    st.session_state.selected_division = get_db_viewer_state()['selected_division']
                
                selected_div = st.multiselect(
                    "Select Division",
                    options=['Food Grocery', 'Fresh_Froz_Food', 'NonFood Grocery', 'Hardlines', 'CH Hardlines', 'Centralized Hardline'],
                    key='selected_division',
                    on_change=lambda: update_db_viewer_state('selected_division', st.session_state.selected_division)
                )

                # Get options for other selectors
                dept_options, section_options, group_options = get_hierarchy_options(
                    initial_df, 
                    selected_div,
                    st.session_state.get('selected_department', []),
                    st.session_state.get('selected_section', [])
                )
                
                col_1, col_2, col_3 = st.columns(3)
                with col_1:
                    selected_dept = st.multiselect(
                        "Select Department",
                        options=dept_options,
                        key='selected_department',
                        on_change=lambda: update_db_viewer_state('selected_department', st.session_state.selected_department)
                    )

                with col_2:
                    selected_section = st.multiselect(
                        "Select Section",
                        options=section_options,
                        key='selected_section',
                        on_change=lambda: update_db_viewer_state('selected_section', st.session_state.selected_section)
                    )      
                with col_3:
                    selected_group = st.multiselect(
                        "Select Group",
                        options=group_options,
                        key='selected_group',
                        on_change=lambda: update_db_viewer_state('selected_group', st.session_state.selected_group)
                    )   
            else:
                # Initialize selected_categories in session state if not present
                if 'selected_categories' not in st.session_state:
                    st.session_state.selected_categories = get_db_viewer_state()['selected_categories']
                
                # Category selection with Select All toggle in a row
                cat_col, toggle_col = st.columns([4, 1])
                with cat_col:
                    selected_cats = st.multiselect(
                        "Select Categories",
                        options=catogeries,
                        key='selected_categories',
                        on_change=handle_category_selection
                    )
                with toggle_col:
                    st.write("")  # Add spacing to align with multiselect
                    st.write("")  # Add spacing to align with multiselect

                    st.toggle('Select All', 
                             key='select_all',
                             value=get_db_viewer_state()['all_selected'],
                             on_change=handle_select_all)
            
            # Product name filter
            product_filter = st.text_input(
                "Filter by Product Name",
                value=get_db_viewer_state()['product_filter'],
                key="product_filter",
                on_change=lambda: update_db_viewer_state('product_filter', st.session_state.product_filter)
            )
        
        with filter_col3:
            st.write("Filter by 100% Values")
            
            package_type_filter = st.pills(
                "Select Repl Type",
                options=["None", "SRP", "NSRP", "Full_pallet", "MU", "Split_pallet"],
                default=[get_db_viewer_state()['package_type_filter']],
                key='package_type_filter',
                selection_mode="single",
                on_change=lambda: update_db_viewer_state('package_type_filter', st.session_state.package_type_filter)
            )
        
        with filter_col2:
            if (use_hierarchy and selected_div) or (not use_hierarchy and selected_cats):
                try:
                    # Load the database based on mode
                    if use_hierarchy:
                        df = load_database_hier(repl_df, 
                                              selected_div,
                                              st.session_state.get('selected_department', []),
                                              st.session_state.get('selected_section', []),
                                              st.session_state.get('selected_group', []))
                    else:
                        df = load_database(repl_df, selected_cats)
                    
                    # Get unique values for filters
                    unique_countries = sorted(df['country'].unique())
                    unique_ownbrands = sorted(df['ownbrand'].unique())
                    
                    # Initialize filter states if not present
                    if 'country_filter' not in st.session_state:
                        st.session_state.country_filter = get_db_viewer_state()['country_filter']
                    if 'ownbrand_filter' not in st.session_state:
                        st.session_state.ownbrand_filter = get_db_viewer_state()['ownbrand_filter']



                    # Country filter
                    country_filter = st.multiselect(
                        "Filter by Country",
                        options=unique_countries,
                        key='country_filter',
                        placeholder="Filter by Country",
                        on_change=lambda: update_db_viewer_state('country_filter', st.session_state.country_filter)
                    )
                    # Own brand filter
                    ownbrand_filter = st.multiselect(
                        "Filter by Own Brand",
                        options=unique_ownbrands,
                        key='ownbrand_filter',
                        placeholder="Filter by Own Brand",
                        on_change=lambda: update_db_viewer_state('ownbrand_filter', st.session_state.ownbrand_filter)
                    )
                    
                    if use_hierarchy:
                        return df, selected_div, product_filter, country_filter, ownbrand_filter, package_type_filter
                    else:
                        return df, selected_cats, product_filter, country_filter, ownbrand_filter, package_type_filter
                except Exception as e:
                    st.error(f"Error loading database: {str(e)}")
                    if use_hierarchy:
                        return None, selected_div, None, [], [], "None"
                    else:
                        return None, selected_cats, None, [], [], "None"
    
    if use_hierarchy:
        return None, selected_div, None, [], [], "None"
    else:
        return None, selected_cats, None, [], [], "None"

def show_database_table(df, filters):
    """Show database table with filters applied"""
    product_filter, country_filter, ownbrand_filter, package_type_filter = filters
    use_hierarchy = get_db_viewer_state()['use_hierarchy']
    
    try:
        # Apply filters
        if product_filter:
            df = df[df['product_name'].str.contains(product_filter, case=False)]
        if country_filter:
            df = df[df['country'].isin(country_filter)]
        if ownbrand_filter:
            df = df[df['ownbrand'].isin(ownbrand_filter)]
        if package_type_filter != "None":
            # Use the numeric column for filtering 100% if available
            numeric_col = f'{package_type_filter.lower()}_numeric'
            if numeric_col in df.columns:
                df = df[df[numeric_col] >= 99.5]  # Consider values ≥ 99.5% as 100%
            else:
                # Fallback to string comparison if numeric columns aren't available
                df = df[df[package_type_filter.lower()] == '100%']
        
        # Add a selection column to the dataframe
        select_all = get_db_viewer_state()['select_all_rows']
        df['selected'] = select_all  # Set all rows to match select_all state
        
        # Add a "Select All" checkbox in the header
        if st.checkbox(
            "Select All Rows",
            value=select_all,
            key='select_all_rows',
            on_change=lambda: update_db_viewer_state('select_all_rows', st.session_state.select_all_rows)
        ):
            df['selected'] = True
        else:
            df['selected'] = False
        
        # Make sure all required columns exist
        # Create a list of columns that are actually in the dataframe
        existing_columns = ['selected']
        for col in df.columns:
            if col != 'selected' and not col.endswith('_numeric'):
                existing_columns.append(col)
        
        # Configure columns based on hierarchy mode
        if use_hierarchy:
            column_config = {
                "selected": st.column_config.CheckboxColumn(
                    "Select",
                    help="Select items to include in the output",
                    default=False,
                    width="small",
                ),
                "product_name": st.column_config.TextColumn(
                    "Product Name",
                    help="Filter updates as you type",
                ),
                "country": st.column_config.TextColumn("Country"),
                "tpnb": st.column_config.NumberColumn("TPNB", format="%d"),
                "division_hier": st.column_config.TextColumn("Division"),
                "department": st.column_config.TextColumn("Department"),
                "section": st.column_config.TextColumn("Section"),
                "group": st.column_config.TextColumn("Group"),
                "subgroup": st.column_config.TextColumn("Subgroup"),
                "pmg": st.column_config.TextColumn("PMG"),
            }
            
            # Add percentage columns only if they exist
            for col in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                if col in df.columns:
                    column_config[col] = st.column_config.NumberColumn(
                        col.upper() if col == 'srp' or col == 'nsrp' else col.replace('_', ' ').title(),
                        format="%.0f%%"
                    )
            
            # Add other columns if they exist
            if 'icream_nsrp' in df.columns:
                column_config['icream_nsrp'] = st.column_config.TextColumn("Icream NSRP")
            if 'sold_units' in df.columns:
                column_config['sold_units'] = st.column_config.NumberColumn("Sold Units")
            if 'cases_delivered' in df.columns:
                column_config['cases_delivered'] = st.column_config.NumberColumn("Cases Delivered")
            if 'ownbrand' in df.columns:
                column_config['ownbrand'] = st.column_config.TextColumn("Own Brand")
            if 'case_capacity' in df.columns:
                column_config['case_capacity'] = st.column_config.TextColumn("Case Capacity")    
            
            # Define column order with only existing columns
            base_columns = ["selected", "country", "division_hier", "department", "section", "group", "subgroup", 
                          "pmg", "tpnb", "product_name", "ownbrand"]
            percentage_columns = [col for col in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet'] if col in df.columns]
            other_columns = [col for col in ['icream_nsrp', 'sold_units', 'cases_delivered', 'case_capacity'] if col in df.columns]
            
            # Combine all columns, but only include those that exist
            column_order = [col for col in base_columns if col in existing_columns]
            column_order.extend([col for col in percentage_columns if col in existing_columns])
            column_order.extend([col for col in other_columns if col in existing_columns])
            
            # All columns except 'selected' should be disabled
            disabled_columns = [col for col in existing_columns if col != 'selected']
        else:
            column_config = {
                "selected": st.column_config.CheckboxColumn(
                    "Select",
                    help="Select items to include in the output",
                    default=False,
                    width="small",
                ),
                "product_name": st.column_config.TextColumn(
                    "Product Name",
                    help="Filter updates as you type",
                ),
                "country": st.column_config.TextColumn("Country"),
                "tpnb": st.column_config.NumberColumn("TPNB", format="%d"),
                "division": st.column_config.TextColumn("Division"),
                "Category name": st.column_config.TextColumn("Category Name"),
                "pmg": st.column_config.TextColumn("PMG"),
            }
            
            # Add percentage columns only if they exist
            for col in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                if col in df.columns:
                    column_config[col] = st.column_config.NumberColumn(
                        col.upper() if col == 'srp' or col == 'nsrp' else col.replace('_', ' ').title(),
                        format="%.0f%%"
                    )
            
            # Add other columns if they exist
            if 'icream_nsrp' in df.columns:
                column_config['icream_nsrp'] = st.column_config.TextColumn("Icream NSRP")
            if 'sold_units' in df.columns:
                column_config['sold_units'] = st.column_config.NumberColumn("Sold Units")
            if 'cases_delivered' in df.columns:
                column_config['cases_delivered'] = st.column_config.NumberColumn("Cases Delivered")
            if 'ownbrand' in df.columns:
                column_config['ownbrand'] = st.column_config.TextColumn("Own Brand")
            if 'case_capacity' in df.columns:
                column_config['case_capacity'] = st.column_config.TextColumn("Case Capacity")  
            
            # Define column order with only existing columns
            base_columns = ["selected", "country", "division", "Category name", "pmg", "tpnb", "product_name", "ownbrand"]
            percentage_columns = [col for col in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet'] if col in df.columns]
            other_columns = [col for col in ['icream_nsrp', 'sold_units', 'cases_delivered', 'case_capacity'] if col in df.columns]
            
            # Combine all columns, but only include those that exist
            column_order = [col for col in base_columns if col in existing_columns]
            column_order.extend([col for col in percentage_columns if col in existing_columns])
            column_order.extend([col for col in other_columns if col in existing_columns])
            
            # All columns except 'selected' should be disabled
            disabled_columns = [col for col in existing_columns if col != 'selected']

        # Create a dataframe with only the columns we want to display
        display_df = df[existing_columns].copy()
        
        # Replace the string percentage values with the numeric values for sorting
        for col in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
            numeric_col = f'{col}_numeric'
            if numeric_col in df.columns and col in display_df.columns:
                display_df[col] = df[numeric_col]

        # Display the dataframe with selection enabled
        edited_df = st.data_editor(
            display_df,
            column_config=column_config,
            disabled=disabled_columns,
            hide_index=True,
            column_order=column_order,
            use_container_width=True,
            height=300,
            key='data_editor'
        )
        
        # Keep the original df for further processing
        # Map the selection back to the original dataframe
        original_df = df.copy()
        original_df['selected'] = False
        for idx in edited_df.index:
            if idx in original_df.index:
                original_df.loc[idx, 'selected'] = edited_df.loc[idx, 'selected']
        
        return original_df
        
    except Exception as e:
        st.error(f"Error loading database: {str(e)}")
        return None

def show_database_summary(df, edited_df):
    """Show database summary section"""
    if edited_df is not None:
        # Get selected data first
        selected_data = get_selected_data(df, edited_df)
        use_hierarchy = get_db_viewer_state()['use_hierarchy']
        
        # Only proceed if there are actual selections
        if selected_data and any(len(tpnbs) > 0 for country, tpnbs in selected_data.items()):
            with st.container(border=False):
                col1, col2 = st.columns([2, 1])

                with col1:
                    with st.container():
                        # Create and display summary table if there are selections
                        summary_df = create_summary_table(df, edited_df)
                        if summary_df is not None and not summary_df.empty:
                            if use_hierarchy:
                                st.write("### Summary of Selected Items by Division and Department")
                            else:
                                st.write("### Summary of Selected Items by Country and Category")
                            st.dataframe(
                                summary_df,
                                use_container_width=True,
                                hide_index=True
                            )
                
                with col2:
                    with st.container(key="add_to_excel_container"):
                        st.write("")
                        st.write("")
                        st.write("")
                        st.write("")
                        st.write("")
                        col1, col2 =st.columns(2)
                        with col1:
                            if st.button("Add to Input file", type="primary", key="add_to_excel", icon=":material/save:",
                                        disabled=st.session_state.get('calculation_running', False)):
                                success, error = export_to_excel(selected_data)
                                if success:
                                    # Update the database viewer state only
                                    update_db_viewer_state('select_all_rows', False)
                                    update_db_viewer_state('use_hierarchy', False)
                                    update_db_viewer_state('selected_division', [])
                                    update_db_viewer_state('selected_department', [])
                                    update_db_viewer_state('selected_section', [])
                                    update_db_viewer_state('selected_group', [])
                                    update_db_viewer_state('selected_categories', [])
                                    update_db_viewer_state('product_filter', "")
                                    update_db_viewer_state('country_filter', [])
                                    update_db_viewer_state('ownbrand_filter', [])
                                    update_db_viewer_state('all_selected', False)
                                    update_db_viewer_state('package_type_filter', "None")

                                    st.success("Data successfully added to Input file!")
                                    time.sleep(2)
                                    # Force a rerun to update the UI
                                    st.rerun()
                                else:
                                    error_msg = str(error)
                                    
                                    # Create user-friendly error messages for common errors
                                    if "Permission denied" in error_msg:
                                        friendly_msg = "⚠️ Error: Excel file is currently open. Please close the file and try again."
                                        steps = """
                                        Close the Excel file if it's open and try again.
                                        """
                                        st.error(f"{friendly_msg}\n{steps}")
                                    else:
                                        st.error(f"Failed to export data: {error_msg}")
                        with col2:
                            if st.button("Visualize Data", type="secondary", key="visualize_data", icon="📊",
                                        disabled=st.session_state.get('calculation_running', False)):
                                show_visualization_dialog(edited_df)

@st.dialog("Data Visualizations")
def show_visualization_dialog(edited_df):
    try:
        with st.container(key="large"):
            
            # Only show visualization if there's selected data
            selected_data = edited_df[edited_df['selected'] == True]
            if not selected_data.empty:
                create_piechart(selected_data)
            else:
                st.warning("No data selected. Please select at least one row in the table.")
    except Exception as e:
        st.error(f"Error creating visualizations: {str(e)}")

def show_database_viewer():
    """Main database viewer component"""
    st.title("Database Viewer")
    st.markdown("##### View and analyze database contents", unsafe_allow_html=True)
    
    # Show filters and get data
    df, selected_items, product_filter, country_filter, ownbrand_filter, package_type_filter = show_database_filters()
    
    use_hierarchy = get_db_viewer_state()['use_hierarchy']
    if (use_hierarchy and selected_items) or (not use_hierarchy and selected_items):
        # Show table with filters applied
        edited_df = show_database_table(df, (product_filter, country_filter, ownbrand_filter, package_type_filter))
        
        # Show summary if we have data
        if df is not None and edited_df is not None:
            show_database_summary(df, edited_df)

def table_height(df):
    """Calculate the height of the DataFrame display"""
    num_rows = len(df)
    row_height = 35  # Approximate height of a row in pixels

    final = num_rows * row_height + 50  # Add some padding

    if final > 300:
        final = 400
    else:
        final
    
        return final

def main():
    if st.session_state.current_step == 0:
        show_step_one()
    elif st.session_state.current_step == 1:
        setup_variables()
    elif st.session_state.current_step == 2:
        show_variables_card()

if __name__ == "__main__":
    main()

def create_summary_table(df, edited_df):
    """Create a summary table of selected TPNBs by country and category"""
    try:
        use_hierarchy = get_db_viewer_state()['use_hierarchy']
        
        # Get only the rows that have been selected by the user
        selected_rows = edited_df[edited_df['selected'] == True].copy()
        
        if not selected_rows.empty:
            if use_hierarchy:
                # Create combined division-department column
                selected_rows['Division_Department'] = selected_rows['division_hier'].astype('str') + ' - ' + selected_rows['department'].astype('str')
                
                # Group by combined column and country
                summary = selected_rows.groupby(['Division_Department', 'country']).agg({
                    'tpnb': 'count'
                }).reset_index()
                
                # Pivot the data to create columns for each country
                summary_pivot = summary.pivot(
                    index='Division_Department',
                    columns='country',
                    values='tpnb'
                ).fillna(0).astype(int)
                
                # Add total column
                summary_pivot['Total'] = summary_pivot.sum(axis=1)
                
                # Reset index to make all columns visible
                summary_pivot = summary_pivot.reset_index()
                
                # Rename columns for clarity
                summary_pivot = summary_pivot.rename(columns={
                    'Division_Department': 'Category'
                })
                
                return summary_pivot
            else:
                # Important: Only include categories that are actually present in the selected rows
                # This is the key change to fix the issue
                categories_in_selection = selected_rows['Category name'].unique()
                
                if len(categories_in_selection) == 0:
                    return None
                    
                # Group by category and country
                summary = selected_rows.groupby(['Category name', 'country']).agg({
                    'tpnb': 'count'
                }).reset_index()
                
                # Pivot the data to create columns for each country
                summary_pivot = summary.pivot(
                    index='Category name',
                    columns='country',
                    values='tpnb'
                ).fillna(0).astype(int)
                
                # Calculate total column
                summary_pivot['Total'] = summary_pivot.sum(axis=1)
                
                # Reset index to make all columns visible
                summary_pivot = summary_pivot.reset_index()
                
                # Rename columns for clarity
                summary_pivot = summary_pivot.rename(columns={
                    'Category name': 'Category'
                })
                
                # Filter to ONLY include categories that are in the selected rows
                summary_pivot = summary_pivot[summary_pivot['Category'].isin(categories_in_selection)]
                
                return summary_pivot
        
        return None
    except Exception as e:
        st.error(f"Error creating summary table: {str(e)}")
        return None

