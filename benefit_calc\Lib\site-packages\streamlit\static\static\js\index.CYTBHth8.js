import{r,aE as f,b2 as l,j as t,b3 as m,bn as B,b1 as b,b4 as p}from"./index.C1NIn1Y2.js";import{c as h}from"./createDownloadLinkElement.DZMwyjvU.js";function w(n,o,e){return h({enforceDownloadInNewTab:e,url:n.buildMediaURL(o),filename:""})}function D(n){const{disabled:o,element:e,widgetMgr:s,endpoints:a,fragmentId:d}=n,{libConfig:{enforceDownloadInNewTab:c=!1}}=r.useContext(f);let i=l.SECONDARY;e.type==="primary"?i=l.PRIMARY:e.type==="tertiary"&&(i=l.TERTIARY),r.useEffect(()=>{a.checkSourceUrlResponse(e.url,"Download Button")},[e.url,a]);const u=()=>{e.ignoreRerun||s.setTriggerValue(e,{fromUi:!0},d),w(a,e.url,c).click()};return t("div",{className:"stDownloadButton","data-testid":"stDownloadButton",children:t(m,{help:e.help,containerWidth:e.useContainerWidth,children:t(B,{kind:i,size:b.SMALL,disabled:o,onClick:u,containerWidth:e.useContainerWidth,children:t(p,{icon:e.icon,label:e.label})})})})}const g=r.memo(D);export{g as default};
