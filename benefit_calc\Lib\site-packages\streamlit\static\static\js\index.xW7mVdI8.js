import{n as k,r as o,aN as H,z as B,co as W,C as j,j as r,bp as O,bD as _,bq as q,b7 as w,br as K,D as N,cp as $}from"./index.C1NIn1Y2.js";import{u as A}from"./uniqueId.O0UbJ2Bu.js";import{u as G,a as J,b as M}from"./useOnInputChange.Cxh6ExEn.js";import{a as Q}from"./useBasicWidgetState.Ci89jaH5.js";import{I as X}from"./InputInstructions.DaZ89mzH.js";import{I as Y}from"./input.CxKZ5Wrc.js";import"./inputUtils.CQWz5UKz.js";import"./FormClearHelper.D1M9GM_c.js";import"./base-input.BJ4qsfSq.js";const Z=k("div",{target:"e1o1zy6o0"})("position:relative;");function tt({disabled:a,element:t,widgetMgr:s,fragmentId:d}){var I;const[l,c]=o.useState(()=>T(s,t)??null),[y,x]=H(),[i,p]=o.useState(!1),V=o.useCallback(()=>{c(t.default??null),p(!0)},[t.default]),[C,m]=Q({getStateFromWidgetMgr:T,getDefaultStateFromProto:et,getCurrStateFromProto:at,updateWidgetMgrState:ot,element:t,widgetMgr:s,fragmentId:d,onFormCleared:V});G(C,l,c,i);const[F,g]=o.useState(!1),e=B(),[b]=o.useState(()=>A("text_input_")),{placeholder:z,formId:u,icon:n,maxChars:f}=t,h=o.useCallback(()=>{p(!1),m({value:l,fromUi:!0})},[l,m]),E=W({formId:u})?s.allowFormEnterToSubmit(u):i,v=F&&y>e.breakpoints.hideWidgetDetails,R=o.useCallback(()=>{i&&h(),g(!1)},[i,h]),D=o.useCallback(()=>{g(!0)},[]),L=J({formId:u,maxChars:f,setDirty:p,setUiValue:c,setValueWithSource:m}),P=M(u,h,i,s,d),S=n==null?void 0:n.startsWith(":material"),U=S?"lg":"base";return j(Z,{className:"stTextInput","data-testid":"stTextInput",ref:x,children:[r(K,{label:t.label,disabled:a,labelVisibility:O((I=t.labelVisibility)==null?void 0:I.value),htmlFor:b,children:t.help&&r(_,{children:r(q,{content:t.help,placement:w.TOP_RIGHT})})}),r(Y,{value:l??"",placeholder:z,onBlur:R,onFocus:D,onChange:L,onKeyPress:P,"aria-label":t.label,disabled:a,id:b,type:st(t),autoComplete:t.autocomplete,startEnhancer:n&&r(N,{"data-testid":"stTextInputIcon",iconValue:n,size:U}),overrides:{Input:{style:{minWidth:0,lineHeight:e.lineHeights.inputWidget,paddingRight:e.spacing.sm,paddingLeft:e.spacing.md,paddingBottom:e.spacing.sm,paddingTop:e.spacing.sm,"::placeholder":{color:e.colors.fadedText60}}},Root:{props:{"data-testid":"stTextInputRootElement"},style:{height:e.sizes.minElementHeight,borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth,paddingLeft:n?e.spacing.sm:0}},StartEnhancer:{style:{paddingLeft:0,paddingRight:0,minWidth:e.iconSizes.lg,color:S?e.colors.fadedText60:"inherit"}}}}),v&&r(X,{dirty:i,value:l??"",maxLength:f,inForm:W({formId:u}),allowEnterToSubmit:E})]})}function T(a,t){return a.getStringValue(t)??null}function et(a){return a.default??null}function at(a){return a.value??null}function ot(a,t,s,d){t.setStringValue(a,s.value,{fromUi:s.fromUi},d)}function st(a){return a.type===$.Type.PASSWORD?"password":"text"}const ht=o.memo(tt);export{ht as default};
