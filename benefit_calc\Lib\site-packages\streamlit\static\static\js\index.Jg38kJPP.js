import{n as c,r as C,aD as S,C as p,j as i,aC as E,l as y}from"./index.C1NIn1Y2.js";import{w as L,E as W}from"./withFullScreenWrapper.iW37lS8Z.js";import{S as h,T as v}from"./Toolbar.KhlcEc0K.js";const b=c("div",{target:"evl31sl0"})(({theme:e})=>({display:"flex",flexDirection:"row",flexWrap:"wrap",rowGap:e.spacing.lg,maxWidth:"100%",width:"fit-content"})),T=c("div",{target:"evl31sl1"})(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"stretch",width:"auto",flexGrow:0,">img":{borderRadius:e.radii.default}})),F=c("div",{target:"evl31sl2"})(({theme:e})=>({textAlign:"center",marginTop:e.spacing.xs,wordWrap:"break-word",padding:e.spacing.threeXS})),j=y.getLogger("ImageList");function D({element:e,endpoints:g,disableFullscreenMode:u}){const{expanded:s,width:w,height:l,expand:x,collapse:f}=S(W),m=w||0;let o;const r=e.width;if([-1,-3,-4].includes(r))o=void 0;else if([-2,-5].includes(r))o=m;else if(r>0)o=r;else throw Error(`Invalid image width: ${r}`);const t={};l&&s?(t.maxHeight=l,t.objectFit="contain",t.width="100%"):(t.width=o??"100%",t.maxWidth="100%");const I=n=>{const a=n.currentTarget.src;j.error(`Client Error: Image source error - ${a}`),g.sendClientErrorToHost("Image","Image source failed to load","onerror triggered",a)};return p(h,{width:m,height:l,useContainerWidth:s,topCentered:!0,children:[i(v,{target:h,isFullScreen:s,onExpand:x,onCollapse:f,disableFullscreenMode:u}),i(b,{className:"stImage","data-testid":"stImage",children:e.imgs.map((n,a)=>{const d=n;return p(T,{"data-testid":"stImageContainer",children:[i("img",{style:t,src:g.buildMediaURL(d.url),alt:a.toString(),onError:I}),d.caption&&i(F,{"data-testid":"stImageCaption",style:t,children:i(E,{source:d.caption,allowHTML:!1,isCaption:!0,isLabel:!0})})]},a)})})]})}const G=L(D),$=C.memo(G);export{$ as default};
