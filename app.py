import streamlit as st
from styles import apply_styles, hide_st_style
import streamlit_antd_components as sac
import logging
import sys

# Ensure Streamlit logs errors
logging.basicConfig(
    level=logging.ERROR,  # Adjust to DEBUG if needed
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("streamlit_app.log"),  # Log to a file
        logging.StreamHandler(sys.stdout),  # Also log to console
    ],
)

# Get Streamlit’s logger and override its settings
streamlit_logger = logging.getLogger("streamlit")
streamlit_logger.setLevel(logging.ERROR)  # Change to DEBUG for more details


# Page config must be the first Streamlit command
st.set_page_config(
    page_title="Replenishment Type Changer",
    layout="wide",
    page_icon='images/t.png'

)
# Apply styles


apply_styles(st)

# st.markdown(hide_st_style, unsafe_allow_html=True)

# Disable navigation during calculation
if st.session_state.get('calculation_running', False):
    st.markdown("""
        <style>
        .st-emotion-cache-1cypcdb {
            pointer-events: none !important;
            opacity: 0.7 !important;
        }
        </style>
    """, unsafe_allow_html=True)

nav_menu = [
    

        st.Page(
            title="Home",
            page="pages/home.py",
            icon=":material/home:",
        ),


        st.Page(
            title="Whatif Calculator",
            page="main.py",
            icon=":material/calculate:",
        )

    

]
nav = st.navigation(nav_menu, position="top")
nav.run()


with st.sidebar:
    st.logo(r"images\Tesco_logo.png")

    st.write("")
    st.write("")
    st.write("")
    st.write("")
    st.write("")
    st.write("")

    st.write("")
