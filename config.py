
IS_PROD = False

if IS_PROD:
    with open("dataset_path.txt", "r") as file:
        REPL_DF_PATH = file.readline().strip()
    OUTPUT_DF_PATH = "/mnt/tbs_enterprise_analytics/SRP_NSRP_dataset/results/output.parquet"
else:
    OUTPUT_DF_PATH = r"\\huprgvmfs07\TBS_Enterprise_Analytics\SRP_NSRP_dataset\results\output.parquet"
    REPL_DF_PATH = "data/Dataset_based_on_plano_0121"

PERIOD = (int(REPL_DF_PATH[-4:-2]) -2) % 12